import sys
import os
import subprocess
import re
import time
import threading
import requests
import json
import random
import string
from datetime import datetime

from PySide6.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
                              QPushButton, QLabel, QTextEdit, QGraphicsColorizeEffect,
                              QPlainTextEdit, QSizePolicy, QSpacerItem, QMessageBox, QTextBrowser)
from PySide6.QtCore import Qt, Signal, QObject, Slot 
from PySide6.QtGui import QClipboard, QFont, QColor, QPixmap,QIcon

# 导入 AUGMENT_LOGO 和 LOGO_DESCRIPTION
from logo import AUGMENT_LOGO

# 导入资源文件
import resources_rc

# 导入现有功能模块
import get_163email
from simpleLogin import login as login_simplelogin, create_random_email, page
from aug import get_vscode_db_path, clean_vscode_db

# 邮箱历史记录文件
EMAIL_HISTORY_FILE = "email_history.json"

# 信号类，用于在线程间通信
class WorkerSignals(QObject):
    log_update = Signal(str)
    operation_complete = Signal(bool, str)  # 成功/失败，结果数据

class AUGMENTAutoGui(QWidget):
    def __init__(self):
        super().__init__()
        
        # 窗口基本设置
        self.setWindowTitle("AUGMENT 自动化工具")
        self.setMinimumSize(600, 500)
        self.setWindowFlags(Qt.WindowStaysOnTopHint)
        # self.setWindowOpacity(0.95)
        
        # 状态变量
        self._is_processing = False
        self.current_email = ""
        self.current_code = ""
        self.browser_launched = False  # 添加浏览器状态跟踪变量
        
        # 初始化信号
        self.signals = WorkerSignals()
        self.signals.log_update.connect(self.update_log)
        self.signals.operation_complete.connect(self.handle_operation_complete)
        
        # 创建UI
        self.init_ui()
    
    def closeEvent(self, event):
        """关闭窗口时确保Chrome浏览器实例被关闭，但只在浏览器已启动时执行"""
        try:
            # 只有在浏览器已经启动过的情况下才尝试关闭它
            if self.browser_launched:
                self.update_log("正在关闭浏览器页面...")
                
                # 使用辅助方法安全关闭浏览器
                try:
                    # 使用self.signals.log_update会导致UI更新问题，所以在closeEvent中使用self.update_log
                    original_log_method = self.signals.log_update
                    self.signals.log_update = self.update_log
                    
                    # 使用辅助方法关闭浏览器
                    browser_closed = self.safe_close_browser(timeout=3)
                    
                    # 恢复原始的日志方法
                    self.signals.log_update = original_log_method
                    
                    if browser_closed:
                        self.browser_launched = False
                        self.update_log("浏览器已成功关闭")
                    else:
                        self.update_log("警告：应用关闭时，浏览器可能未完全关闭")
                        # 最后一次尝试强制关闭
                        try:
                            if sys.platform == 'win32':
                                subprocess.run("taskkill /f /im chrome.exe", shell=True, timeout=1)
                            elif sys.platform == 'darwin':  # MacOS
                                subprocess.run("pkill -f 'Google Chrome'", shell=True, timeout=1)
                            elif sys.platform.startswith('linux'):  # Linux
                                subprocess.run("pkill -f chrome", shell=True, timeout=1)
                        except:
                            pass
                except Exception as e:
                    self.update_log(f"关闭浏览器时出错: {str(e)}")
            else:
                self.update_log("应用关闭，未检测到浏览器启动记录")
        except Exception as e:
            print(f"关闭应用时出错: {str(e)}")
        
        # 调用父类的closeEvent以确保正常关闭窗口
        super().closeEvent(event)
    
    def init_ui(self):
        # 主布局
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)
        
        # 标题区域 - 水平布局包含两侧图标和中间标题
        title_layout = QHBoxLayout()
        title_layout.setAlignment(Qt.AlignCenter)
        
        # 左侧图标
        left_icon = QLabel()
        left_pixmap = QPixmap(":/icon/1.png")
        # 缩放图标到合适大小
        scaled_left_pixmap = left_pixmap.scaled(64, 64, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        left_icon.setPixmap(scaled_left_pixmap)
        left_icon.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        title_layout.addWidget(left_icon)
        
        # 添加一些间距
        title_layout.addSpacing(10)
        
        # 中间标题文本
        title_label = QLabel("victor-Augment-Pro")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont("Arial", 16, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #FFD700; margin: 10px;") # 金色标题
        title_layout.addWidget(title_label)
        
        # 添加一些间距
        title_layout.addSpacing(10)
        
        # 右侧图标
        right_icon = QLabel()
        right_pixmap = QPixmap(":/icon/2.png")
        # 缩放图标到合适大小
        scaled_right_pixmap = right_pixmap.scaled(64, 64, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        right_icon.setPixmap(scaled_right_pixmap)
        right_icon.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        title_layout.addWidget(right_icon)
        
        # 将标题布局添加到主布局
        main_layout.addLayout(title_layout)
        
        # 添加副标题 - 免费使用
        subtitle_label = QLabel("🎁 免费使用")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_font = QFont("Arial", 12)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setStyleSheet("color: #4CAF50; margin-bottom: 15px;") # 绿色副标题
        main_layout.addWidget(subtitle_label)
        
        # 按钮区域 - 水平布局
        button_layout = QHBoxLayout()
        
        # 按钮样式
        button_style_blue = """
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #0b7dda;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
                color: #666666;
            }
        """
        
        button_style_green = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #3e8e41;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
                color: #666666;
            }
        """
        
        button_style_orange = """
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #e68a00;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
                color: #666666;
            }
        """
        
        # 自动注册按钮 - 蓝色
        self.register_button = QPushButton("自动注册")
        self.register_button.clicked.connect(self.on_register_clicked)
        self.register_button.setStyleSheet(button_style_blue)
        button_layout.addWidget(self.register_button)
        
        # 获取验证码按钮 - 绿色
        self.verify_button = QPushButton("获取验证码")
        self.verify_button.clicked.connect(self.on_verify_clicked)
        self.verify_button.setStyleSheet(button_style_green)
        button_layout.addWidget(self.verify_button)
        
        # 执行aug按钮 - 橙色
        self.reset_button = QPushButton("执行aug")
        self.reset_button.clicked.connect(self.on_aug_clicked)
        self.reset_button.setStyleSheet(button_style_orange)
        button_layout.addWidget(self.reset_button)
        
        main_layout.addLayout(button_layout)
        
        # 结果显示区域
        result_layout = QHBoxLayout()
        
        # 结果标签
        self.result_label = QLabel("结果:")
        self.result_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        result_layout.addWidget(self.result_label)
        
        # 结果值
        self.result_value = QLabel("")
        self.result_value.setStyleSheet("font-size: 14px;")
        result_layout.addWidget(self.result_value)
        
        # 复制按钮
        self.copy_button = QPushButton("复制")
        self.copy_button.clicked.connect(self.copy_result)
        self.copy_button.setEnabled(False)
        self.copy_button.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
                color: #666666;
            }
        """)
        result_layout.addWidget(self.copy_button)
        
        main_layout.addLayout(result_layout)
        
        # 日志区域
        log_label = QLabel("操作日志:")
        log_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-top: 10px;")
        main_layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 5px;
                font-family: Consolas, Monaco, monospace;
            }
        """)
        main_layout.addWidget(self.log_text)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setAlignment(Qt.AlignBottom | Qt.AlignLeft)
        self.status_label.setStyleSheet("color: #2196F3; font-weight: bold;")
        main_layout.addWidget(self.status_label)
        
        # 添加简洁的 logo 描述 - 只包含关键信息
        footer_layout = QVBoxLayout()
        
        footer_line1 = QLabel("🚀 1、关闭AUGMENT、退出账号、官网Setting删除账号，刷新节点IP")
        footer_line2 = QLabel("💼 2、执行aug操作：清理VSCode数据库中的augment相关条目")
        footer_line3 = QLabel("📞 作者网站: https://www.allfather.top | QQ群: 738066261")
        
        footer_style = "color: #666; font-size: 12px;"
        footer_line1.setStyleSheet(footer_style)
        footer_line2.setStyleSheet(footer_style)
        footer_line3.setStyleSheet(footer_style)
        
        footer_layout.addWidget(footer_line1)
        footer_layout.addWidget(footer_line2)
        footer_layout.addWidget(footer_line3)
        
        main_layout.addLayout(footer_layout)
        
        # 灰度效果初始化
        self.gray_effect = QGraphicsColorizeEffect()
        self.gray_effect.setColor(Qt.lightGray)
        self.setGraphicsEffect(self.gray_effect)
        self.gray_effect.setEnabled(False)

        # 显示启动logo
        self.show_startup_logo()
    
    def update_log(self, message):
        """更新日志区域的文本"""
        current_time = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{current_time}] {message}"
        self.log_text.append(log_message)
        # 自动滚动到底部
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )

    def add_log_without_timestamp(self, message):
        """添加不带时间戳的日志消息"""
        self.log_text.append(message)
        # 自动滚动到底部
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )

    def show_startup_logo(self):
        """在程序启动时显示logo"""
        # 显示ASCII艺术logo
        logo_lines = AUGMENT_LOGO.strip().split('\n')
        for line in logo_lines:
            self.add_log_without_timestamp(line)

        # 添加空行和欢迎消息
        self.add_log_without_timestamp("")
        self.update_log("程序启动完成，欢迎使用 victor-Augment-Pro！")
        self.update_log("请选择需要执行的操作...")
    
    def handle_operation_complete(self, success, result):
        """处理操作完成信号"""
        if success:
            if "email" in result.lower() or "@" in result:  # 判断是邮箱结果
                self.current_email = result
                self.result_label.setText("邮箱:")
                self.result_value.setText(result)
                self.copy_button.setEnabled(True)
                self.update_log("注册成功! 请从AUGMENT应用中退出账户，并使用此邮箱开始登录")
            elif len(result) <= 8 and result.isdigit():  # 判断是验证码结果
                self.current_code = result
                self.result_label.setText("验证码:")
                self.result_value.setText(result)
                self.copy_button.setEnabled(True)
                self.update_log("获取验证码成功! 请使用此验证码完成登录")
            else:  # aug操作结果
                self.result_label.setText("结果:")
                self.result_value.setText("aug执行成功")
                self.copy_button.setEnabled(False)
                self.update_log("aug操作执行成功! VSCode数据库已清理完成")
        else:
            self.update_log(f"操作失败: {result}")
            self.result_label.setText("错误:")
            self.result_value.setText(result)
            self.copy_button.setEnabled(False)
        
        self.enable_buttons_and_disable_effect()
    
    def disable_buttons_and_enable_effect(self):
        """禁用所有按钮，并启用灰度效果"""
        self.register_button.setEnabled(False)
        self.verify_button.setEnabled(False)
        self.reset_button.setEnabled(False)
        self.copy_button.setEnabled(False)
        self._is_processing = True
        QApplication.setOverrideCursor(Qt.WaitCursor)
        self.status_label.setText("操作中...")
        QApplication.processEvents()
    
    def enable_buttons_and_disable_effect(self):
        """启用所有按钮，并禁用灰度效果"""
        self.register_button.setEnabled(True)
        self.verify_button.setEnabled(True)
        self.reset_button.setEnabled(True)
        # 复制按钮根据是否有结果决定是否启用
        if self.current_email or self.current_code:
            self.copy_button.setEnabled(True)
        self._is_processing = False
        QApplication.restoreOverrideCursor()
        self.status_label.setText("就绪")
        QApplication.processEvents()
    
    def copy_result(self):
        """复制结果到剪贴板"""
        clipboard = QApplication.clipboard()
        
        if self.result_label.text() == "邮箱:":
            clipboard.setText(self.current_email)
            self.update_log("邮箱已复制到剪贴板")
        elif self.result_label.text() == "验证码:":
            clipboard.setText(self.current_code)
            self.update_log("验证码已复制到剪贴板")
        else:
            clipboard.setText(self.result_value.text())
            self.update_log("结果已复制到剪贴板")
    
    def on_register_clicked(self):
        """自动注册按钮点击事件"""
        if not self._is_processing:
            self.disable_buttons_and_enable_effect()
            self.update_log("开始自动注册...")
            # 在新线程中执行注册操作
            threading.Thread(target=self.register_account, daemon=True).start()
    
    def on_verify_clicked(self):
        """获取验证码按钮点击事件"""
        if not self._is_processing:
            if not self.current_email:
                QMessageBox.warning(self, "警告", "请先完成自动注册获取邮箱!")
                return
                
            self.disable_buttons_and_enable_effect()
            self.update_log("开始获取验证码...")
            # 在新线程中执行获取验证码操作
            threading.Thread(target=self.get_verification_code, daemon=True).start()
    
    def on_aug_clicked(self):
        """执行aug按钮点击事件"""
        if not self._is_processing:
            # 显示确认对话框
            confirm_dialog = QMessageBox(self)
            confirm_dialog.setWindowTitle("执行aug确认")
            confirm_dialog.setIcon(QMessageBox.Question)
            confirm_dialog.setText("重要提示：执行aug操作")
            confirm_dialog.setInformativeText("此操作将清理VSCode数据库中包含'augment'关键字的条目。\n\n继续执行aug操作吗？")
            confirm_dialog.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            confirm_dialog.setDefaultButton(QMessageBox.No)

            # 设置按钮文本为中文
            confirm_dialog.button(QMessageBox.Yes).setText("继续")
            confirm_dialog.button(QMessageBox.No).setText("取消")

            # 显示对话框并获取用户选择
            choice = confirm_dialog.exec_()

            if choice == QMessageBox.Yes:
                # 用户确认继续执行
                self.disable_buttons_and_enable_effect()
                self.update_log("开始执行aug操作...")
                # 在新线程中执行aug操作
                threading.Thread(target=self.execute_aug, daemon=True).start()
            else:
                # 用户取消操作
                self.update_log("aug操作已取消")
    
    def save_email_history(self, email):
        """保存邮箱到历史记录文件"""
        try:
            # 读取现有历史记录
            history = []
            if os.path.exists(EMAIL_HISTORY_FILE):
                try:
                    with open(EMAIL_HISTORY_FILE, 'r') as f:
                        history = json.load(f)
                except json.JSONDecodeError:
                    # 文件损坏，创建新文件
                    history = []
            
            # 添加新邮箱记录
            history.append({
                "email": email,
                "date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
            
            # 写入文件
            with open(EMAIL_HISTORY_FILE, 'w') as f:
                json.dump(history, f, indent=4)
                
            self.signals.log_update.emit(f"邮箱 {email} 已保存到历史记录")
            return True
        except Exception as e:
            self.signals.log_update.emit(f"保存邮箱历史记录失败: {str(e)}")
            return False
    
    def register_account(self):
        """注册账户功能 - 使用simpleLogin.py的功能"""
        try:
            # 使用SimpleLogin服务创建随机邮箱
            self.signals.log_update.emit("正在登录SimpleLogin服务...")
            
            # 登录SimpleLogin
            try:
                login_simplelogin()
                self.browser_launched = True  # 设置浏览器已启动标志
                self.signals.log_update.emit("SimpleLogin登录成功")
            except Exception as e:
                self.signals.log_update.emit(f"SimpleLogin登录失败: {str(e)}")
                self.signals.operation_complete.emit(False, f"登录失败: {str(e)}")
                return
            
            # 创建随机邮箱
            self.signals.log_update.emit("正在创建随机邮箱...")
            try:
                email = create_random_email()
                self.signals.log_update.emit(f"随机邮箱创建成功: {email}")
            except Exception as e:
                self.signals.log_update.emit(f"创建随机邮箱失败: {str(e)}")
                self.signals.operation_complete.emit(False, f"创建邮箱失败: {str(e)}")
                return
            
            # 保存邮箱到历史记录
            self.save_email_history(email)
            
            # 关闭浏览器页面
            self.signals.log_update.emit("正在关闭浏览器页面...")
            browser_closed = self.safe_close_browser(timeout=5)
            if browser_closed:
                self.browser_launched = False  # 浏览器已成功关闭，重置标志
            else:
                self.signals.log_update.emit("警告：浏览器可能未完全关闭，但将继续执行")
            
            # 返回邮箱给UI线程
            self.signals.operation_complete.emit(True, email)
            
        except Exception as e:
            self.signals.log_update.emit(f"注册过程发生错误: {str(e)}")
            self.signals.operation_complete.emit(False, str(e))
            # 确保浏览器页面被关闭
            try:
                browser_closed = self.safe_close_browser(timeout=3)
                if browser_closed:
                    self.browser_launched = False  # 重置浏览器标志
            except:
                pass
    
    def get_verification_code(self):
        """获取验证码功能 - 使用get_163email.py的功能"""
        try:
            self.signals.log_update.emit(f"正在获取邮箱 {self.current_email} 的验证码...")
            
            # 使用163邮箱服务获取验证码
            try:
                mail_client = get_163email.Email()
                self.signals.log_update.emit("已连接到163邮箱服务")
                
                # 尝试获取验证码，最多尝试5次
                verification_code = None
                attempts = 0
                max_attempts = 5
                
                while attempts < max_attempts and not verification_code:
                    attempts += 1
                    self.signals.log_update.emit(f"正在检查邮件，第 {attempts} 次尝试...")
                    verification_code = mail_client.get_verification_code()
                    
                    if verification_code:
                        break
                    elif attempts < max_attempts:
                        self.signals.log_update.emit("未找到验证码，等待5秒后重试...")
                        time.sleep(5)
                
                if verification_code:
                    self.signals.log_update.emit("验证码获取成功")
                else:
                    self.signals.log_update.emit("未能找到验证码，请检查邮箱或重试")
                    self.signals.operation_complete.emit(False, "未找到验证码")
                    return
                
            except Exception as e:
                self.signals.log_update.emit(f"连接邮箱服务失败: {str(e)}")
                self.signals.operation_complete.emit(False, f"获取验证码失败: {str(e)}")
                return
            
            # 返回验证码给UI线程
            self.signals.operation_complete.emit(True, verification_code)
            
        except Exception as e:
            self.signals.log_update.emit(f"获取验证码过程发生错误: {str(e)}")
            self.signals.operation_complete.emit(False, str(e))
    
    def execute_aug(self):
        """执行aug功能 - 清理VSCode数据库中包含'augment'关键字的条目"""
        try:
            self.signals.log_update.emit("开始执行aug操作...")
            self.signals.log_update.emit("正在清理VSCode数据库中的augment相关条目...")

            # 调用aug.py中的clean_vscode_db函数
            try:
                clean_vscode_db()
                self.signals.log_update.emit("aug操作执行成功")
                self.signals.log_update.emit("✅ VSCode数据库中的augment相关条目已清理完成")
                self.signals.operation_complete.emit(True, "Aug Successful")

            except Exception as e:
                self.signals.log_update.emit(f"执行aug操作时出错: {str(e)}")
                self.signals.log_update.emit("❌ aug操作执行失败")
                self.signals.operation_complete.emit(False, f"aug操作失败: {str(e)}")

        except Exception as e:
            self.signals.log_update.emit(f"aug操作过程发生错误: {str(e)}")
            self.signals.operation_complete.emit(False, str(e))
    
    def safe_close_browser(self, timeout=5):
        """安全关闭浏览器的辅助方法，包含超时机制"""
        # 只在需要时导入page
        try:
            from simpleLogin import page
        except ImportError as e:
            self.signals.log_update.emit(f"导入浏览器模块失败: {str(e)}")
            return False
        except Exception as e:
            self.signals.log_update.emit(f"导入浏览器模块时发生错误: {str(e)}")
            return False
            
        browser_closed = False
        
        # 定义线程函数
        def close_browser():
            nonlocal browser_closed
            try:
                page.close()
                browser_closed = True
            except Exception as e:
                self.signals.log_update.emit(f"关闭浏览器页面时内部错误: {str(e)}")
        
        # 创建线程关闭浏览器
        close_thread = threading.Thread(target=close_browser)
        close_thread.daemon = True
        close_thread.start()
        
        # 等待指定时间
        close_thread.join(timeout)
        
        if browser_closed:
            self.signals.log_update.emit("浏览器页面已正常关闭")
            return True
        else:
            self.signals.log_update.emit("浏览器页面关闭超时，尝试强制关闭...")
            # 强制关闭浏览器进程
            force_closed = False
            try:
                if sys.platform == 'win32':
                    subprocess.run("taskkill /f /im chrome.exe", shell=True)
                    force_closed = True
                elif sys.platform == 'darwin':  # MacOS
                    subprocess.run("pkill -f 'Google Chrome'", shell=True)
                    force_closed = True
                elif sys.platform.startswith('linux'):  # Linux
                    subprocess.run("pkill -f chrome", shell=True)
                    force_closed = True
                
                if force_closed:
                    self.signals.log_update.emit("已强制关闭所有Chrome进程")
                    return True
            except Exception as e:
                self.signals.log_update.emit(f"强制关闭Chrome进程时出错: {str(e)}")
                return False
        
        return False


if __name__ == "__main__":
    app = QApplication(sys.argv)
    # 设置应用样式
    app.setStyle("Fusion")
    app.setWindowIcon(QIcon(":/icon/vip.png"))
    window = AUGMENTAutoGui()
    window.setWindowTitle("Victor Augment Pro")  # 设置窗口标题更加美观
    window.show()
    sys.exit(app.exec()) 


#编译qrc
# pyside6-rcc resources.qrc -o resources_rc.py
# 加密
# pyarmor gen AugmentAutoGui.py aug.py get_163email.py main.py simpleLogin.py
# 打包
# pyinstaller --clean --add-data "pyarmor_runtime_000000;."  --hidden-import=PySide6.QtCore --hidden-import=DrissionPage.common --hidden-import=PySide6.QtGui --hidden-import=DrissionPage --hidden-import=PySide6.QtWidgets --hidden-import=imaplib --hidden-import=logger --hidden-import=logo --hidden-import=main --hidden-import=resources_rc --hidden-import=simpleLogin  --add-data "get_163email.py;."  --add-data "aug.py;." --add-data "logger.py;." --add-data "logo.py;." --add-data "main.py;." --add-data "resources_rc.py;." --add-data "simpleLogin.py;." --add-data "main.png;." --icon=main.png --windowed --name=AugmentHelper --strip --onefile AugmentAutoGui.py
