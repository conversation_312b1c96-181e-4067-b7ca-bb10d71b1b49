import os
import sys

def get_new_name(old_name: str, mode: str, prefix_suffix: str = None, old_str: str = None, new_str: str = None) -> str | None:
    """
    根据操作模式生成新的名称。

    :param old_name: 原始名称
    :param mode: 操作模式 ('1': 前缀, '2': 后缀, '3': 替换)
    :param prefix_suffix: 用于添加的前缀或后缀字符串
    :param old_str: 用于替换的旧字符串
    :param new_str: 用于替换的新字符串
    :return: 新的名称，如果无需更改则返回 None
    """
    if mode == '1':  # 添加前缀
        return prefix_suffix + old_name
        
    elif mode == '2':  # 添加后缀
        # 分离主文件名和扩展名
        name, extension = os.path.splitext(old_name)
        # 将后缀添加到主文件名后
        return f"{name}{prefix_suffix}{extension}"
        
    elif mode == '3':  # 替换
        if old_str in old_name:
            return old_name.replace(old_str, new_str)
        # 如果旧字符串不存在于名称中，则不进行任何操作
        return None
        
    return None

def batch_rename_recursive(root_path: str, mode: str, **kwargs):
    """
    以“由深到浅”的方式递归地重命名文件和文件夹。

    :param root_path: 要处理的根目录路径
    :param mode: 操作模式
    :param kwargs: 包含操作所需参数的字典
    """
    print("\n--- 🚀 开始处理 ---")
    renamed_count = 0
    
    # 使用 os.walk 并设置 topdown=False，从最深的子目录开始向上处理
    for dirpath, dirnames, filenames in os.walk(root_path, topdown=False):
        
        # 1. 重命名当前目录下的文件
        for filename in filenames:
            new_filename = get_new_name(filename, mode, **kwargs)
            if new_filename:
                old_filepath = os.path.join(dirpath, filename)
                new_filepath = os.path.join(dirpath, new_filename)
                
                if old_filepath != new_filepath:
                    try:
                        if os.path.exists(new_filepath):
                            print(f"⚠️ 跳过文件: 目标 '{new_filepath}' 已存在。")
                            continue
                        os.rename(old_filepath, new_filepath)
                        print(f"📄 文件: '{filename}' -> '{new_filename}'")
                        renamed_count += 1
                    except OSError as e:
                        print(f"❌ 文件重命名错误 '{old_filepath}': {e}")

        # 2. 重命名当前目录下的子文件夹
        for dirname in dirnames:
            new_dirname = get_new_name(dirname, mode, **kwargs)
            if new_dirname:
                old_dirpath = os.path.join(dirpath, dirname)
                new_dirpath = os.path.join(dirpath, new_dirname)

                if old_dirpath != new_dirpath:
                    try:
                        if os.path.exists(new_dirpath):
                            print(f"⚠️ 跳过文件夹: 目标 '{new_dirpath}' 已存在。")
                            continue
                        os.rename(old_dirpath, new_dirpath)
                        print(f"📁 文件夹: '{dirname}' -> '{new_dirname}'")
                        renamed_count += 1
                    except OSError as e:
                        print(f"❌ 文件夹重命名错误 '{old_dirpath}': {e}")
                        
    print(f"\n--- ✅ 处理完成！总共重命名了 {renamed_count} 个项目。 ---")


def main():
    """
    程序主函数，负责用户交互。
    """
    print("--- 批量重命名工具 ---")
    
    # 1. 获取有效的目标文件夹路径
    while True:
        target_path = input("请输入要处理的根文件夹路径: ").strip()
        if os.path.isdir(target_path):
            break
        else:
            print("错误: 路径无效或不是一个文件夹，请重新输入。")

    # 2. 获取操作模式
    print("\n请选择要执行的操作:")
    print("  1. 添加前缀")
    print("  2. 添加后缀 (添加到扩展名前)")
    print("  3. 替换名称中的特定文本")
    while True:
        mode = input("请输入操作编号 (1/2/3): ").strip()
        if mode in ['1', '2', '3']:
            break
        else:
            print("输入无效，请输入 1, 2, 或 3。")

    # 3. 根据模式获取相应参数
    params = {}
    if mode == '1':
        params['prefix_suffix'] = input("请输入要添加的前缀: ").strip()
    elif mode == '2':
        params['prefix_suffix'] = input("请输入要添加的后缀: ").strip()
    elif mode == '3':
        params['old_str'] = input("请输入要被替换的旧文本: ").strip()
        params['new_str'] = input("请输入用来替换的新文本: ").strip()
        if not params['old_str']:
            print("错误: 要被替换的旧文本不能为空。")
            sys.exit(1)

    # 4. 最终确认
    print("\n" + "="*40)
    print("🚨 请检查并确认以下操作 🚨")
    print(f"  - 目标路径: {target_path}")
    if mode == '1':
        print(f"  - 操作类型: 添加前缀 '{params['prefix_suffix']}'")
    elif mode == '2':
        print(f"  - 操作类型: 添加后缀 '{params['prefix_suffix']}'")
    else:
        print(f"  - 操作类型: 将 '{params['old_str']}' 替换为 '{params['new_str']}'")
    print("="*40)
    
    confirm = input("此操作不可撤销，是否确定要执行? (y/n): ").strip().lower()
    
    if confirm == 'y':
        batch_rename_recursive(target_path, mode, **params)
    else:
        print("操作已取消。")

if __name__ == "__main__":
    main()

