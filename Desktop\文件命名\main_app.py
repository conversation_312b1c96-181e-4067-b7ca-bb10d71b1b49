# 1. 导入必要的模块
import sys
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, 
    QVBoxLayout, QHBoxLayout, QLabel, 
    QLineEdit, QPushButton
)
from PySide6.QtCore import Qt # 用于对齐等常量

# 2. 创建主窗口类
class MainWindow(QMainWindow):
    """
    我们的主窗口。所有应用的UI元素和逻辑都在这里定义。
    """
    def __init__(self):
        # 调用父类的构造函数
        super().__init__()

        # --- 窗口基本设置 ---
        self.setWindowTitle("我的第一个PySide6应用") # 设置窗口标题
        self.setGeometry(100, 100, 400, 200) # 设置窗口位置和大小 (x, y, width, height)

        # --- 创建核心控件 ---
        # 标签控件，用于提示用户输入
        self.name_label = QLabel("请输入你的名字:")
        
        # 单行文本输入框控件
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("例如：小明") # 设置占位提示符

        # 按钮控件
        self.greet_button = QPushButton("向我问好")
        
        # 用于显示结果的标签
        self.output_label = QLabel("...")
        self.output_label.setAlignment(Qt.AlignmentFlag.AlignCenter) # 居中对齐
        self.output_label.setStyleSheet("font-size: 16px; color: blue;") # 设置字体样式

        # --- 信号与槽的连接 ---
        # 这是GUI应用的核心：当某个事件发生时（信号），执行某个函数（槽）
        # .clicked 是按钮被点击的信号
        # .connect() 方法将信号连接到一个槽函数
        self.greet_button.clicked.connect(self.show_greeting_message)
        # 当在输入框中按回车时，也触发同样的事件
        self.name_input.returnPressed.connect(self.show_greeting_message)

        # --- 设置布局 ---
        # 布局用于管理窗口中控件的排列方式
        # QVBoxLayout 是垂直布局，会让控件从上到下排列
        main_layout = QVBoxLayout()
        
        # 将控件添加到布局中
        main_layout.addWidget(self.name_label)
        main_layout.addWidget(self.name_input)
        main_layout.addWidget(self.greet_button)
        main_layout.addWidget(self.output_label)

        # 创建一个中央小部件，并应用主布局
        # QMainWindow 需要一个中央小部件来承载布局
        central_widget = QWidget()
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

    # 3. 定义槽函数 (Slot)
    def show_greeting_message(self):
        """
        当问候按钮被点击时，此函数会被调用。
        """
        # 从输入框获取文本
        user_name = self.name_input.text().strip()

        # 根据输入内容设置输出标签的文本
        if user_name:
            message = f"你好, {user_name}! 欢迎使用PySide6。"
            self.output_label.setText(message)
        else:
            self.output_label.setText("请输入你的名字后再试！")
        
# 4. 应用程序的入口
if __name__ == "__main__":
    # 每个PySide应用都需要一个QApplication实例
    app = QApplication(sys.argv)
    
    # 创建主窗口的实例
    window = MainWindow()
    
    # 显示窗口
    window.show()
    
    # 启动应用的事件循环，并确保程序可以干净地退出
    sys.exit(app.exec())